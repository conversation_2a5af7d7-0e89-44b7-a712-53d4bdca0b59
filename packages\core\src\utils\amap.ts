import type {
  AMapGeocoder,
  AMapGeolocation,
  AMapGeolocationOptions,
  AMapMap,
  AMapMapOptions,
} from '../types/amap'

// 错误类型定义
export class AMapError extends Error {
  constructor(
    message: string,
    public code: string,
  ) {
    super(message)
    this.name = 'AMapError'
  }
}

// 日志级别
type LogLevel = 'error' | 'warn'

// 日志记录函数（仅记录错误和警告）
const log = (level: LogLevel, message: string, ...args: unknown[]) => {
  const timestamp = new Date().toISOString()
  const prefix = `[AMap][${level.toUpperCase()}] ${timestamp}`

  switch (level) {
    case 'warn':
      console.warn(prefix, message, ...args)
      break
    case 'error':
      console.error(prefix, message, ...args)
      break
  }
}

// 全局状态
let isInitialized = false
let geolocation: AMapGeolocation | null = null
let geocoder: AMapGeocoder | null = null
let mapInstance: AMapMap | null = null
let initializationPromise: Promise<{
  geolocation: AMapGeolocation | null
  geocoder: AMapGeocoder | null
}> | null = null

// 初始化配置
const AMAP_CONFIG = {
  version: '2.0',
  plugins: ['AMap.Geolocation', 'AMap.Geocoder', 'AMap.Scale'],
  geolocationOptions: {
    enableHighAccuracy: true,
    timeout: 10000,
    showButton: false,
    showMarker: false,
    showCircle: false,
  } as AMapGeolocationOptions,
}

export const initializeAMap = async (): Promise<{
  geolocation: AMapGeolocation | null
  geocoder: AMapGeocoder | null
}> => {
  if (isInitialized) {
    return { geolocation, geocoder }
  }

  // 如果正在初始化，返回同一个 Promise
  if (initializationPromise) {
    return initializationPromise
  }

  const apiKey = process.env.NEXT_PUBLIC_AMAP_KEY

  if (!apiKey) {
    const error = new AMapError('高德地图配置缺失', 'CONFIG_MISSING')
    log('error', error.message, { apiKey: !!apiKey })
    return { geolocation: null, geocoder: null }
  }

  initializationPromise = (async () => {
    try {
      // 设置安全代理（动态获取当前域名）
      const serviceHost = `${window.location.origin}/_AMapService/`
      window._AMapSecurityConfig = {
        serviceHost,
      }

      // 检查是否已经加载了 AMapLoader
      if (!window.AMapLoader) {
        await new Promise<void>((resolve, reject) => {
          const script = document.createElement('script')
          script.src = 'https://webapi.amap.com/loader.js'
          script.async = true
          script.onload = () => {
            resolve()
          }
          script.onerror = (error) => {
            const err = new AMapError('AMap Loader 脚本加载失败', 'SCRIPT_LOAD_FAILED')
            log('error', err.message, error)
            reject(err)
          }
          document.head.appendChild(script)
        })
      }

      const AMap = await window.AMapLoader.load({
        key: apiKey,
        version: AMAP_CONFIG.version,
        plugins: AMAP_CONFIG.plugins,
      })

      geolocation = new AMap.Geolocation(AMAP_CONFIG.geolocationOptions)
      geocoder = new AMap.Geocoder()
      isInitialized = true
      return { geolocation, geocoder }
    } catch (error) {
      const err = new AMapError(
        error instanceof Error ? error.message : '高德地图初始化失败',
        'INITIALIZATION_FAILED',
      )
      log('error', err.message, error)
      return { geolocation: null, geocoder: null }
    }
  })()

  return initializationPromise
}

// 获取地图实例
export const getMapInstance = async (
  container: string,
  options: AMapMapOptions,
): Promise<AMapMap | null> => {
  if (!isInitialized) {
    await initializeAMap()
  }

  try {
    // 每次都创建新的地图实例
    mapInstance = new window.AMap.Map(container, options)
    mapInstance.addControl(new window.AMap.Scale())
    return mapInstance
  } catch (error) {
    console.error('创建地图实例失败:', error)
    return null
  }
}

// 重置高德地图实例（用于测试或重新初始化）
export const resetAMapInstance = () => {
  isInitialized = false
  geolocation = null
  geocoder = null
  mapInstance = null
  initializationPromise = null
}
