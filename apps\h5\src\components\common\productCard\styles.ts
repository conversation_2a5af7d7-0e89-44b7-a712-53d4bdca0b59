const FONT_FAMILY = {
  bold: 'font-miSansSemibold520',
  medium: 'font-miSansDemiBold450',
  regular: 'font-miSansRegular330',
}

const styles = {
  container: 'rounded-[12px] mb-[28px] overflow-hidden',
  containerStyle: {
    width: '100%',
  },
  tagWrapper:
    'absolute top-0 w-16 h-8 bg-[#DA291C] rounded-[100px] my-[4px] ml-[4px] flex justify-center items-center',
  tag: 'font-miSansMedium380 text-[10px] leading-[12px] text-[#FFFFFF]',
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    backgroundColor: '#F3F3F4',
    overflow: 'hidden',
    aspectRatio: '1/1',
  },
  text: 'pl-[2px]',
  textStyle: {
    width: '100%',
  },
  typeWrapper: 'mt-[12px] h-[12px]',
  type: `text-[10px] leading-[12px] text-[#6E6E73]`,
  nameWrapper: 'mt-[6px]',
  name: `text-[14px] leading-[17px] text-[#000000] line-clamp-2`,
  customPriceText: `text-[14px] leading-[17px]`,
  customText: `${FONT_FAMILY.bold} text-[12px] leading-[14px]`,
  flexRowCenter: 'flex flex-row items-center justify-start mt-[12px]',
  customUnderLineText: `${FONT_FAMILY.bold} text-[12px] leading-[14px] mt-[2px]`,
  status:
    'absolute bottom-0 w-full h-[28px] z-10 rounded-b-[8px] flex items-center justify-center backdrop-blur-[10px] bg-[#00000014]',
  statusText: `${FONT_FAMILY.regular} text-[12px] leading-[100%] text-[#222223]`,
}

export default styles
