/**
 * useNavigate Hook
 *
 * 这是一个自定义导航 Hook，用于统一处理应用内的页面跳转逻辑。
 * 支持多种导航场景：
 * 1. 普通路由导航
 * 2. WebView 页面导航
 * 3. 动态 URL 导航
 * 4. 带参数的路由导航
 * 5. 带查询参数的路由导航
 * 6. 无历史记录的页面跳转
 */

import { useCallback } from 'react'

import i18nRouting from '../config/i18n.config'
import createNextNavigation from '../i18n/navigation'

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const { useRouter } = createNextNavigation(i18nRouting)

import { ROUTE, URL_TYPE_ROUTE } from '../constants'
import { getShopApiUrl } from '../utils/envUtil'

// 定义查询参数类型
type QueryParams = Record<string, string | number | boolean | object | undefined | null>

const useNavigate = () => {
  const router = useRouter()

  const shopApiUrl = getShopApiUrl()

  /**
   * 构建带查询参数的 URL
   * @param path - 基础路径
   * @param params - 查询参数对象
   * @returns 完整的 URL 字符串
   */
  const buildUrlWithQueryParams = useCallback((path: string, params?: QueryParams) => {
    if (!params) return path

    const queryString = Object.entries(params)
      .filter(([, value]) => value !== undefined && value !== null)
      .map(([key, value]) => {
        let encodedValue: string
        if (typeof value === 'object') {
          // 对象类型参数转为JSON字符串
          try {
            encodedValue = encodeURIComponent(JSON.stringify(value))
          } catch (e) {
            console.error('Failed to stringify object:', e)
            encodedValue = encodeURIComponent('[object]')
          }
        } else {
          // 原始类型直接转字符串
          encodedValue = encodeURIComponent(String(value))
        }
        return `${encodeURIComponent(key)}=${encodedValue}`
      })
      .join('&')

    return queryString ? `${path}?${queryString}` : path
  }, [])

  /**
   * 验证 URL 是否有效
   * @param url - 需要验证的 URL 字符串
   * @returns boolean - URL 是否有效
   */
  const isValidUrl = useCallback((url: string) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }, [])

  /**
   * 处理 WebView 导航
   * 在新标签页中打开外部链接
   * @param url - 需要打开的 URL
   */
  const handleWebViewNavigate = useCallback(
    (url: string) => {
      if (isValidUrl(url)) {
        window.open(url, '_blank')
      } else {
        console.error('Invalid URL provided for webView')
      }
    },
    [isValidUrl],
  )

  /**
   * 根据 replace 参数决定使用 push 还是 replace 进行导航
   * @param path - 目标路径
   * @param shouldReplace - 是否使用 replace 模式
   */
  const navigate = useCallback(
    (path: string, shouldReplace: boolean) => {
      if (shouldReplace) {
        router.replace(path)
      } else {
        router.push(path)
      }
    },
    [router],
  )

  /**
   * 统一的页面导航方法
   * @param params - 导航参数对象
   * @param params.route - 目标路由名称
   * @param params.type - URL 类型
   * @param params.url - 目标 URL
   * @param params.value - 路由参数值
   * @param params.isBack - 是否为返回操作
   * @param params.from - 来源页面
   * @param params.webViewUrl - WebView 页面 URL
   * @param params.queryParams - 查询参数对象
   * @param params.replace - 是否使用 replace 模式
   */
  const openPage = useCallback(
    ({
      route = '',
      type = '',
      url = '',
      value = '',
      isBack = false,
      from = '',
      webViewUrl = '',
      queryParams = {},
      replace = false,
    }: {
      route?: string
      type?: string | null
      url?: string | null
      value?: string | number | null
      isBack?: boolean
      from?: string
      webViewUrl?: string
      queryParams?: QueryParams
      replace?: boolean
    }) => {
      // 验证必要参数
      if (!route && !type) {
        return
      }

      // 确定目标路由
      const newRoute =
        route || URL_TYPE_ROUTE[type as keyof typeof URL_TYPE_ROUTE] || URL_TYPE_ROUTE.DEFAULT

      // 处理 WebView 特殊场景
      if (newRoute === ROUTE.webView) {
        const targetUrl = webViewUrl || url || value
        if (typeof targetUrl === 'string' && targetUrl.startsWith('http')) {
          handleWebViewNavigate(targetUrl)
          return
        }
        return
      }

      // 路由映射表：定义所有支持的路由及其处理函数
      const routeMapping: { [key: string]: (value?: string) => void } = {
        // 基础页面路由
        [ROUTE.home]: () => navigate(buildUrlWithQueryParams('/', queryParams), replace),
        [ROUTE.search]: () => navigate(buildUrlWithQueryParams('/search', queryParams), replace),
        [ROUTE.checkoutCart]: () =>
          navigate(buildUrlWithQueryParams('/checkout/cart', queryParams), replace),
        [ROUTE.checkout]: () =>
          navigate(buildUrlWithQueryParams('/checkout', queryParams), replace),
        [ROUTE.checkoutResult]: () =>
          navigate(buildUrlWithQueryParams('/checkout/result', queryParams), replace),
        [ROUTE.checkoutPaying]: () =>
          navigate(buildUrlWithQueryParams('/checkout/paying', queryParams), replace),
        // 分类相关路由
        [ROUTE.catalogCategoryAll]: () =>
          navigate(
            buildUrlWithQueryParams(
              `/category-all/${encodeURIComponent(value || '')}`,
              queryParams,
            ),
            replace,
          ),
        [ROUTE.catalogCategory]: () => {
          if (!url) return
          const baseUrl = url.replace(shopApiUrl || '', '')
          navigate(buildUrlWithQueryParams(baseUrl, queryParams), replace)
        },
        [ROUTE.catalogProduct]: () => {
          if (!url) return
          const baseUrl = url.replace(shopApiUrl || '', '')
          navigate(buildUrlWithQueryParams(baseUrl, queryParams), replace)
        },

        // CMS 页面路由
        [ROUTE.cmsPage]: () => {
          if (!url) return
          const baseUrl = url.replace(shopApiUrl || '', '')
          navigate(buildUrlWithQueryParams(baseUrl, queryParams), replace)
        },

        // 用户账户相关路由
        [ROUTE.account]: () =>
          navigate(buildUrlWithQueryParams('/customer/account', queryParams), replace),
        [ROUTE.accountAddress]: () => {
          const baseParams = {
            from: from || '',
            ...(isBack ? { isBack } : {}),
            ...queryParams,
          }
          navigate(buildUrlWithQueryParams('/customer/addresses', baseParams), replace)
        },
        [ROUTE.accountAddressAdd]: () =>
          navigate(buildUrlWithQueryParams('/customer/addresses/add', queryParams), replace),
        [ROUTE.accountAddressEdit]: () =>
          navigate(
            buildUrlWithQueryParams(
              `/customer/addresses/edit/${encodeURIComponent(value || '')}`,
              queryParams,
            ),
            replace,
          ),

        // 订单相关路由
        [ROUTE.accountCoupon]: () =>
          navigate(buildUrlWithQueryParams('/customer/coupons', queryParams), replace),
        [ROUTE.accountOrder]: () =>
          navigate(buildUrlWithQueryParams('/customer/orders', queryParams), replace),
        [ROUTE.accountOrderDetail]: () =>
          navigate(buildUrlWithQueryParams('/customer/orders/detail', queryParams), replace),
        [ROUTE.accountOrderReturn]: () =>
          navigate(buildUrlWithQueryParams('/customer/returns', queryParams), replace),
        [ROUTE.accountOrderReturnDetail]: () =>
          navigate(buildUrlWithQueryParams('/customer/returns/detail', queryParams), replace),
        [ROUTE.accountOrderReturnTrack]: () =>
          navigate(buildUrlWithQueryParams('/customer/returns/track', queryParams), replace),
        [ROUTE.pcOrderReturn]: () =>
          navigate(
            buildUrlWithQueryParams('/customer/orders/detail/returns', queryParams),
            replace,
          ),

        // 搜索相关路由
        [ROUTE.searchResult]: () => {
          const baseParams = {
            q: value || '',
            ...queryParams,
          }
          navigate(buildUrlWithQueryParams('/search/result', baseParams), replace)
        },

        // 专题页面路由
        [ROUTE.thematicArea]: () =>
          navigate(
            buildUrlWithQueryParams(`/thematic/${encodeURIComponent(value || '')}`, queryParams),
            replace,
          ),

        // WebView 路由
        [ROUTE.webView]: () => {
          if (url && isValidUrl(url)) {
            window.location.href = url
          } else {
            console.error('Invalid URL provided for webView')
          }
        },

        // 默认路由
        [URL_TYPE_ROUTE.DEFAULT]: () => navigate('/', replace),
      }

      // 执行路由跳转
      try {
        const routeHandler = routeMapping[newRoute]
        if (routeHandler) {
          routeHandler(String(value))
        } else {
          // 如果没有找到对应的路由处理器，尝试直接跳转到 URL
          navigate(url || '/', replace)
        }
      } catch (error) {
        console.error('Error navigating to the specified route:', error)
        // 发生错误时跳转到首页
        navigate('/', replace)
      }
    },
    [buildUrlWithQueryParams, handleWebViewNavigate, isValidUrl, navigate, shopApiUrl],
  )

  return {
    openPage,
  }
}

export default useNavigate
