'use client'

import { useEffect, useRef, useState } from 'react'
import ReactPlayer from 'react-player'

import { Play } from '@/components'

// 扩展 Document 和 HTMLElement 接口以支持全屏 API
declare global {
  interface Document {
    webkitExitFullscreen?: () => Promise<void>
    msExitFullscreen?: () => Promise<void>
  }

  interface HTMLElement {
    webkitRequestFullscreen?: () => Promise<void>
    msRequestFullscreen?: () => Promise<void>
  }
}

//图标
const IconVolumeMute = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" rx="16" fill="black" fillOpacity="0.4" />
    <path
      d="M10.1426 19.9721C9.7553 19.959 9.44531 19.6409 9.44531 19.2503L9.44531 12.7503C9.44531 12.3515 9.76866 12.0281 10.1675 12.0281L12.8288 12.0281L16.2534 9.63088C16.474 9.47645 16.7622 9.45758 17.0011 9.58195C17.2399 9.70632 17.3898 9.95325 17.3898 10.2225V12.725L10.1426 19.9721Z"
      fill="white"
    />
    <path
      d="M20.7375 14.4841L19.5415 15.6802C19.6207 16.4583 19.3849 17.2625 18.834 17.8858C18.768 17.9605 18.7674 18.0746 18.838 18.1451L19.604 18.9111C19.6745 18.9816 19.7893 18.9817 19.8568 18.9084C20.9854 17.6832 21.279 15.9655 20.7375 14.4841Z"
      fill="white"
    />
    <path
      d="M22.8537 12.368L21.7931 13.4285C22.9018 15.5552 22.5971 18.2271 20.8791 20.0542C20.8108 20.1268 20.8102 20.2412 20.8808 20.3118L21.6468 21.0778C21.7173 21.1483 21.8322 21.1481 21.9009 21.0758C24.1839 18.6777 24.5014 15.0955 22.8537 12.368Z"
      fill="white"
    />
    <path
      d="M17.3898 17.8319L14.2525 20.9692L16.2534 22.3698C16.474 22.5242 16.7622 22.5431 17.0011 22.4187C17.2399 22.2943 17.3898 22.0474 17.3898 21.7781V17.8319Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.533 23.1562L23.2701 10.4191L22.2488 9.39773L9.51161 22.1349L10.533 23.1562Z"
      fill="white"
    />
  </svg>
)
const IconVolumeUnmute = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" rx="16" fill="black" fillOpacity="0.4" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.3898 21.7778C17.3898 22.0471 17.2399 22.294 17.0011 22.4184C16.7622 22.5427 16.474 22.5239 16.2534 22.3694L12.8288 19.9722H10.1675C9.76866 19.9722 9.44531 19.6489 9.44531 19.25L9.44531 12.75C9.44531 12.3511 9.76866 12.0278 10.1675 12.0278L12.8288 12.0278L16.2534 9.63055C16.474 9.47612 16.7622 9.45726 17.0011 9.58162C17.2399 9.70599 17.3898 9.95292 17.3898 10.2222L17.3898 21.7778ZM20.7531 20.1838C23.0094 17.9274 23.0094 14.2691 20.7531 12.0127L21.7745 10.9914C24.5949 13.8118 24.5949 18.3847 21.7745 21.2051L20.7531 20.1838ZM18.7103 18.0171C19.8385 16.8889 19.8385 15.0598 18.7103 13.9316L19.7317 12.9102C21.4239 14.6025 21.4239 17.3462 19.7317 19.0385L18.7103 18.0171Z"
      fill="white"
    />
  </svg>
)

const IconFullScreen = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" rx="16" fill="black" fillOpacity="0.4" />
    <path
      d="M20.8297 18.1978H22.6654L22.607 22.6663H18.2247V20.834L20.8284 20.8327V18.1978H20.8297ZM11.1639 18.1978V20.8327H13.7689V22.6663H9.33203V18.1978H11.1639ZM13.7689 9.33301V11.1654H11.1639V13.8016H9.33203V9.33301H13.7689ZM22.6616 9.33301V13.8016H20.8297L20.8284 11.1654H18.2247V9.33301H22.6616Z"
      fill="white"
    />
  </svg>
)

const IconExitFullScreen = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" rx="16" fill="black" fillOpacity="0.4" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.5619 14.1997H9.33203L9.33203 12.1908L12.4173 12.1894L12.4173 9.33398L14.5619 9.33398V14.1997ZM17.4733 17.7663V22.6668H19.6173L19.6188 19.7758H22.6661V17.7663H17.4733Z"
      fill="white"
    />
  </svg>
)

const IconPause = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="32" height="32" rx="16" fill="black" fillOpacity="0.4" />
    <rect
      width="2.66667"
      height="10.6667"
      transform="matrix(1 0 0 -1 10.668 21.333)"
      fill="white"
    />
    <rect
      width="2.66667"
      height="10.6667"
      transform="matrix(1 0 0 -1 18.668 21.333)"
      fill="white"
    />
  </svg>
)

interface VideoPlayerProps {
  videoUrl?: string
  posterUrl: string
  withMute?: boolean // 是否显示静音按钮
  initialMuted?: boolean // 初始静音状态
  onVideoRef?: (ref: HTMLVideoElement | null) => void // 视频引用回调
}

const VideoPlayer = ({
  videoUrl,
  posterUrl,
  withMute = true,
  initialMuted = true,
  onVideoRef,
}: VideoPlayerProps) => {
  const [videoLoading, setVideoLoading] = useState(true)
  const [videoError, setVideoError] = useState(false)
  // 若初始为静音，则尝试自动播放（Safari 仅允许静音自动播放）
  const [videoPaused, setVideoPaused] = useState(() => !initialMuted)
  const [isMuted, setIsMuted] = useState(initialMuted)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isClient, setIsClient] = useState(false) // 添加客户端检测

  // 进度条相关状态
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isDragging, setIsDragging] = useState(false)

  const playerRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const secureVideoUrl = videoUrl
  const securePosterUrl = posterUrl

  // 处理视频播放/暂停切换
  const handleToggleVideoPlay = () => {
    if (!playerRef.current) return

    const videoElement = playerRef.current
    if (!videoElement) return

    if (videoElement.paused) {
      videoElement.play().catch((error) => {
        console.log('视频播放失败:', error)
        setVideoError(true)
        setVideoPaused(true) // 播放失败时保持暂停状态
      })
      setVideoPaused(false) // 手动更新状态
    } else {
      videoElement.pause()
      setVideoPaused(true) // 手动更新状态
    }
  }

  // 客户端检测
  useEffect(() => {
    setIsClient(true)
  }, [])

  // 监听视频元素的状态并同步到组件状态
  useEffect(() => {
    if (!isClient || !playerRef.current) return

    const videoElement = playerRef.current
    if (!videoElement) return

    // 传递视频引用给父组件
    onVideoRef?.(videoElement)

    // 创建一个函数来同步视频状态
    const syncVideoState = () => {
      if (videoElement.paused) {
        setVideoPaused(true)
      } else {
        setVideoPaused(false)
      }
    }

    // 更新进度的函数
    const updateProgress = () => {
      if (!isDragging) {
        setCurrentTime(videoElement.currentTime)
      }
    }

    // 更新时长的函数
    const updateDuration = () => {
      setDuration(videoElement.duration)
    }

    // 监听视频的各种状态变化
    videoElement.addEventListener('pause', syncVideoState)
    videoElement.addEventListener('play', syncVideoState)
    videoElement.addEventListener('canplay', syncVideoState)
    videoElement.addEventListener('loadeddata', syncVideoState)
    videoElement.addEventListener('timeupdate', updateProgress)
    videoElement.addEventListener('loadedmetadata', updateDuration)
    videoElement.addEventListener('durationchange', updateDuration)

    // 初始同步一次
    syncVideoState()
    if (videoElement.duration) {
      updateDuration()
    }

    // 增加一个延时检查，确保状态正确同步
    const timer = setTimeout(() => {
      syncVideoState()
      if (videoElement.duration) {
        updateDuration()
      }
    }, 300)

    return () => {
      // 清理监听器
      videoElement.removeEventListener('pause', syncVideoState)
      videoElement.removeEventListener('play', syncVideoState)
      videoElement.removeEventListener('canplay', syncVideoState)
      videoElement.removeEventListener('loadeddata', syncVideoState)
      videoElement.removeEventListener('timeupdate', updateProgress)
      videoElement.removeEventListener('loadedmetadata', updateDuration)
      videoElement.removeEventListener('durationchange', updateDuration)
      clearTimeout(timer)
      // 清理时传递 null 给父组件
      onVideoRef?.(null)
    }
  }, [onVideoRef, secureVideoUrl, isClient, isDragging])

  useEffect(() => {
    if (!isClient || !playerRef.current || !containerRef.current) return

    const videoElement = playerRef.current
    if (!videoElement) return

    if (!isMuted) return
  }, [isClient, isMuted, secureVideoUrl])

  // ReactPlayer事件处理
  const handlePlayerReady = () => {
    // 就绪即取消 loading，播放由受控属性 playing 决定，避免与手动 play 冲突
    setVideoLoading(false)
  }

  const handlePlayerError = (error: unknown) => {
    console.error('ReactPlayer error:', error)
    setVideoError(true)
    setVideoLoading(false)
  }

  const handlePlayerPlay = () => {
    setVideoPaused(false)
    setVideoLoading(false)
  }

  const handlePlayerPause = () => {
    setVideoPaused(true)
  }

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 处理进度条拖拽
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
    if (!playerRef.current || !duration) return

    const videoElement = playerRef.current
    const rect = e.currentTarget.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const progressPercent = clickX / rect.width
    const newTime = progressPercent * duration

    videoElement.currentTime = newTime
    setCurrentTime(newTime)
  }

  // 处理进度条拖拽开始
  const handleProgressMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation()
    setIsDragging(true)
  }

  // 处理静音切换
  const handleToggleMute = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!playerRef.current) return

    const videoElement = playerRef.current
    if (!videoElement) return

    const nextMuted = !isMuted
    setIsMuted(nextMuted)
    videoElement.muted = nextMuted
  }

  // 处理全屏切换
  const handleToggleFullscreen = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!containerRef.current) return

    if (!isFullscreen) {
      // 进入全屏
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen()
      } else if (containerRef.current.webkitRequestFullscreen) {
        containerRef.current.webkitRequestFullscreen()
      } else if (containerRef.current.msRequestFullscreen) {
        containerRef.current.msRequestFullscreen()
      }
    } else {
      // 退出全屏
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    }
  }

  // 监听全屏状态变化
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement)
    }

    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('msfullscreenchange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('msfullscreenchange', handleFullscreenChange)
    }
  }, [])

  // 监听全局鼠标事件处理进度条拖拽
  useEffect(() => {
    if (!isDragging) return

    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (!playerRef.current || !duration) return

      const progressBar = document.querySelector('.progress-bar') as HTMLElement
      if (!progressBar) return

      const rect = progressBar.getBoundingClientRect()
      const clickX = e.clientX - rect.left
      const progressPercent = Math.max(0, Math.min(1, clickX / rect.width))
      const newTime = progressPercent * duration

      const videoElement = playerRef.current
      videoElement.currentTime = newTime
      setCurrentTime(newTime)
    }

    const handleGlobalMouseUp = () => {
      setIsDragging(false)
    }

    document.addEventListener('mousemove', handleGlobalMouseMove)
    document.addEventListener('mouseup', handleGlobalMouseUp)

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, duration])

  return (
    <div
      ref={containerRef}
      className={`relative mx-auto bg-[#0F0F0F] ${
        isFullscreen ? 'fixed inset-0 z-50' : 'responsive-product-gallery aspect-square'
      }`}
      style={{ borderRadius: '20px', overflow: 'hidden' }}>
      {/* 加载状态 */}
      {videoLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent" />
        </div>
      )}

      {/* 错误提示 */}
      {videoError && (
        <div className="absolute inset-0 flex items-center justify-center text-gray-500">
          视频加载失败
        </div>
      )}

      {/* 视频播放控制层 - 只覆盖视频播放区域 */}
      <div
        className="absolute bottom-0 left-0 right-0 top-0 z-30 cursor-pointer"
        onClick={handleToggleVideoPlay}>
        {/* 暂停状态时显示播放图标 */}
        {videoPaused && !videoLoading && !videoError && (
          <div className="flex h-full w-full items-center justify-center">
            <Play size={48} />
          </div>
        )}
      </div>

      {/* 自定义进度条和控制条 */}
      <div className="absolute bottom-0 left-0 right-0 z-40 py-base-16">
        {/* 进度条容器 */}
        <div className="mb-base-12 flex flex-col gap-2">
          {/* 进度条 */}
          <div
            className="progress-bar relative h-1 w-full cursor-pointer rounded-full bg-white bg-opacity-10"
            onClick={handleProgressClick}
            onMouseDown={handleProgressMouseDown}>
            {/* 已播放进度 */}
            <div
              className="absolute left-0 top-0 h-full rounded-full bg-[#DA291C]"
              style={{
                width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%',
              }}
            />
          </div>
        </div>

        {/* 控制按钮容器 */}
        <div className="flex items-center justify-between px-base-24">
          {/* 左侧控制区域 */}
          <div className="flex items-center gap-1">
            {/* 播放/暂停按钮 */}
            <button
              className="rounded-full bg-black bg-opacity-40 p-1 text-white backdrop-blur-md"
              onClick={handleToggleVideoPlay}
              aria-label={videoPaused ? '播放' : '暂停'}>
              {videoPaused ? <Play size={32} /> : <IconPause />}
            </button>

            {/* 时间显示 */}
            <div className="ml-1 flex items-center gap-1 text-xs text-white">
              <span>{formatTime(currentTime)}</span>
              <span className="text-white text-opacity-45">/</span>
              <span className="text-white text-opacity-45">{formatTime(duration)}</span>
            </div>
          </div>

          {/* 右侧控制区域 */}
          <div className="flex items-center gap-4">
            {/* 静音控制按钮 */}
            {withMute && (
              <button
                className="rounded-full bg-black bg-opacity-40 p-1 text-white backdrop-blur-md"
                onClick={handleToggleMute}
                aria-label={isMuted ? '取消静音' : '静音'}>
                {isMuted ? <IconVolumeMute /> : <IconVolumeUnmute />}
              </button>
            )}

            {/* 全屏控制按钮 */}
            <button
              className="rounded-full bg-black bg-opacity-40 p-1 text-white backdrop-blur-md"
              onClick={handleToggleFullscreen}
              aria-label={isFullscreen ? '退出全屏' : '全屏'}>
              {isFullscreen ? <IconExitFullScreen /> : <IconFullScreen />}
            </button>
          </div>
        </div>
      </div>

      {isClient ? (
        <ReactPlayer
          ref={playerRef}
          src={secureVideoUrl}
          className="custom-video-player"
          style={{
            aspectRatio: isFullscreen ? 'auto' : '1/1',
            width: '100%',
            height: '100%',
            position: 'relative',
            zIndex: 20,
          }}
          width="100%"
          height="100%"
          playing={!videoPaused}
          muted={isMuted}
          loop
          playsInline
          controls={false}
          wrapper="div"
          onLoadedData={handlePlayerReady}
          onError={handlePlayerError}
          onPlay={handlePlayerPlay}
          onPause={handlePlayerPause}
          onWaiting={() => setVideoLoading(true)}
          onPlaying={() => setVideoLoading(false)}
          poster={securePosterUrl}
          preload="metadata"
        />
      ) : (
        // 服务端渲染时显示占位符
        <div
          className="custom-video-player flex h-full w-full items-center justify-center bg-black object-contain"
          style={{
            aspectRatio: isFullscreen ? 'auto' : '1/1',
            width: '100%',
            height: '100%',
            position: 'relative',
            zIndex: 20,
            backgroundImage: `url(${securePosterUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}>
          {/* 播放按钮占位符 */}
          <div className="flex items-center justify-center">
            <Play size={48} />
          </div>
        </div>
      )}
    </div>
  )
}

export default VideoPlayer
