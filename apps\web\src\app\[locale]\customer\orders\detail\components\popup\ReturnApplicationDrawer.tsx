'use client'

import { useEffect, useMemo, useRef, useState } from 'react'
import Image from 'next/image'
import { useTranslations } from 'next-intl'
import {
  LocalToastProvider,
  OrderReturnFormatItem,
  resolveCatchMessage,
  selectUserAddressById,
  sleep,
  useApplyOrderReturnMutation,
  useDebounceFn,
  useLoadingContext,
  useLocalToastContext,
  userAddressDefaultSelector,
  userOrderReturnAddressIdSelector,
  userOrderReturnIdSelector,
  userOrderSelectedProductsSelector,
  useUserAddress,
} from '@ninebot/core'
import { OrderRequisitionInput } from '@ninebot/core/src/graphql/generated/graphql'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button, Checkbox, Input, Upload } from 'antd'
import type { UploadFile } from 'antd/es/upload/interface'

import { CustomDrawer, Location } from '@/components'

import { useOrderDetail } from '../../context/orderDetailContext'

import { AddressManagementDrawer } from './index'

const MAX_UPLOAD_COUNT = 9 // 最大上传数量

type ImageParams = {
  name?: string
  base64_encoded_file?: string
}

type ImageItem = UploadFile & ImageParams

interface ReturnApplicationDrawerProps {
  visible: boolean
  onClose: () => void
}

const CloseIcon = ({ onClick }: { onClick?: () => void }) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
      style={{ cursor: 'pointer' }}>
      <circle cx="12" cy="12" r="10" fill="black" fillOpacity="0.4" />
      <path
        d="M6.62618 7.81914L7.80469 6.64062L11.4472 10.2832L11.4472 11.4618L10.2687 11.4618L6.62618 7.81914Z"
        fill="white"
      />
      <path
        d="M17.368 7.81914L16.1895 6.64062L12.5469 10.2832L12.5469 11.4618L13.7254 11.4618L17.368 7.81914Z"
        fill="white"
      />
      <path
        d="M6.62618 16.1848L7.80469 17.3633L11.4472 13.7207L11.4472 12.5422L10.2687 12.5422L6.62618 16.1848Z"
        fill="white"
      />
      <path
        d="M17.368 16.1848L16.1895 17.3633L12.5469 13.7207L12.5469 12.5422L13.7254 12.5422L17.368 16.1848Z"
        fill="white"
      />
    </svg>
  )
}

// 内部组件，使用 LocalToastProvider 的上下文
function ReturnApplicationContent({ visible, onClose }: ReturnApplicationDrawerProps) {
  const getI18nString = useTranslations('Common')
  const toast = useLocalToastContext()
  const loading = useLoadingContext()

  const { handleReturnOrder, isFromReturnPopup, isShowAddressByShippingMethod, refetch } =
    useOrderDetail()

  const REASONS = [
    getI18nString('seven_days_without_reason'),
    getI18nString('brought_wrong'),
    getI18nString('not_want_anymore'),
    getI18nString('quality_problem'),
    getI18nString('manual_service'),
    getI18nString('other'),
  ]

  const { fetchUserAddresses } = useUserAddress()
  const addressId = useAppSelector(userOrderReturnAddressIdSelector)
  const orderNumber = useAppSelector(userOrderReturnIdSelector)
  const selectedProducts = useAppSelector(userOrderSelectedProductsSelector)
  const address = useAppSelector((state) => selectUserAddressById(state, String(addressId)))
  const userAddressDefault = useAppSelector(userAddressDefaultSelector)

  const [reason, setReason] = useState('')
  const [description, setDescription] = useState('')
  const [uploadFile, setUploadFile] = useState<ImageItem[]>([])
  const [imageParams, setImageParams] = useState<ImageParams[]>([])
  const [uploading, setUploading] = useState(false)
  // 记录当前处理中的文件名，避免重复上传
  const [processingFiles, setProcessingFiles] = useState<Set<string>>(new Set())

  // 地址管理弹窗状态
  const [isAddressManagementOpen, setIsAddressManagementOpen] = useState(false)

  const [applyOrderReturn] = useApplyOrderReturnMutation()

  /**
   * 寄件地址
   */
  const shippingAddress = useMemo(() => {
    return address || userAddressDefault
  }, [address, userAddressDefault])

  /**
   * 寄件地址 ID
   */
  const shippingAddressId = useMemo(() => {
    return shippingAddress?.address_id
  }, [shippingAddress])

  /**
   * 获取退货参数
   */
  const getReturnParams = () => {
    // 直接从uploadFile获取最新的图片数据
    const latestImageParams = uploadFile
      .filter((item) => item.base64_encoded_file)
      .map((item) => ({
        name: item.name,
        base64_encoded_file: item.base64_encoded_file,
      }))

    // 构建参数对象
    const params: {
      orderNumber: string
      reason: string
      address_id: string | number
      items: OrderReturnFormatItem[]
      comment: string
      images: ImageParams[]
    } = {
      orderNumber: orderNumber || '',
      reason: reason,
      address_id: '0',
      items: [],
      comment: '',
      // 优先使用直接从uploadFile提取的图片数据
      images: latestImageParams.length > 0 ? latestImageParams : imageParams,
    }

    if (isShowAddressByShippingMethod && shippingAddressId) {
      params.address_id = shippingAddressId
    }

    if (selectedProducts?.length > 0) {
      params.items = selectedProducts
    }

    if (description) {
      params.comment = description
    }

    return params
  }

  /**
   * 提交退货
   */
  const { run: handleCancelOrder } = useDebounceFn(() => {
    if (!orderNumber) {
      toast.show({
        content: getI18nString('fetch_data_error'),
        icon: 'fail',
      })
      return
    }
    if (!reason) {
      toast.show({
        icon: 'fail',
        content: getI18nString('select_at_least_one_reason'),
      })
      return
    }

    const params = getReturnParams()

    // 检查是否有上传的图片但没有包含在提交参数中
    if (uploadFile.length > 0 && params.images.length === 0) {
      const directImageParams = uploadFile
        .map((file) => ({
          name: file.name,
          base64_encoded_file: file.base64_encoded_file,
        }))
        .filter((img) => !!img.base64_encoded_file)

      if (directImageParams.length > 0) {
        params.images = directImageParams
      }
    }

    loading.show()
    applyOrderReturn({
      input: params as OrderRequisitionInput,
    })
      .unwrap()
      .then(async (res) => {
        loading.hide()
        await sleep(500)
        if (res?.orderRequisition?.status) {
          toast.show({
            content: getI18nString('application_submitted'),
            icon: 'success',
          })
          await sleep(500)
          onClose()
          await sleep(500)
          refetch()
        } else {
          toast.show({
            icon: 'fail',
            content: res?.orderRequisition?.message || getI18nString('fetch_data_error'),
          })
        }
      })
      .catch(async (error) => {
        loading.hide()
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error) as string,
        })
      })
  })

  /**
   * 处理文件上传
   */
  const handleUpload = async (file: File): Promise<ImageItem> => {
    // 检查文件是否已在处理中
    if (processingFiles.has(file.name)) {
      return Promise.reject(new Error('文件已在处理中'))
    }

    // 添加到处理中文件列表
    setProcessingFiles((prev) => {
      const newSet = new Set(prev)
      newSet.add(file.name)
      return newSet
    })

    setUploading(true)

    try {
      // 创建一个新的Promise来处理文件读取
      const base64Data = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader()

        reader.onload = () => {
          try {
            if (reader.result && typeof reader.result === 'string') {
              // 从data:image/jpeg;base64,/9j/4AAQ... 格式中提取base64编码部分
              const base64 = reader.result.split(',')[1]
              if (!base64) {
                reject(new Error('无法提取base64数据'))
                return
              }
              resolve(base64)
            } else {
              reject(new Error('读取结果无效'))
            }
          } catch (err) {
            reject(err)
          }
        }

        reader.onerror = () => {
          reject(new Error('读取文件失败'))
        }

        reader.readAsDataURL(file)
      })

      // 检查是否已经有相同文件
      const existingFile = uploadFile.find(
        (item) => item.name === file.name && item.size === file.size,
      )

      if (existingFile) {
        return existingFile as ImageItem
      }

      // 构建结果对象
      const result: ImageItem = {
        uid: `upload-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,
        name: file.name,
        status: 'done',
        url: URL.createObjectURL(file), // 创建本地预览URL
        size: file.size,
        type: file.type,
        base64_encoded_file: `base64,${base64Data}`,
      }

      // 一次性更新所有状态，减少重渲染
      const newParam = {
        name: file.name,
        base64_encoded_file: `base64,${base64Data}`,
      }

      setUploadFile((prev) => {
        // 确保不重复添加
        if (prev.some((item) => item.uid === result.uid)) {
          return prev
        }
        return [...prev, result]
      })

      setImageParams((prev) => {
        // 确保不重复添加
        if (
          prev.some(
            (item) =>
              item.name === newParam.name &&
              item.base64_encoded_file === newParam.base64_encoded_file,
          )
        ) {
          return prev
        }
        return [...prev, newParam]
      })

      return result
    } catch (error) {
      throw error
    } finally {
      // 从处理中文件列表移除
      setProcessingFiles((prev) => {
        const newSet = new Set(prev)
        newSet.delete(file.name)
        return newSet
      })
      setUploading(false)
    }
  }

  // 删除文件
  const handleRemove = (file: UploadFile) => {
    // 从处理中文件列表移除
    setProcessingFiles((prev) => {
      const newSet = new Set(prev)
      newSet.delete(file.name as string)
      return newSet
    })

    // 更新文件列表
    const newFileList = uploadFile.filter((item) => item.uid !== file.uid)
    setUploadFile(newFileList)

    // 同步更新imageParams
    const newImageParams = imageParams.filter((item) => {
      // 通过文件名匹配
      return item.name !== file.name
    })
    setImageParams(newImageParams)

    return true
  }

  /**
   * 修改地址 - 打开地址管理弹窗
   */
  const { run: handleChangeAddress } = useDebounceFn(() => {
    setIsAddressManagementOpen(true)
  })

  /**
   * 进入退款页获取最新地址信息
   */
  useEffect(() => {
    if (visible) {
      fetchUserAddresses()
    }
  }, [fetchUserAddresses, visible])

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      toast.show({
        icon: 'fail',
        content: '只能上传图片文件！',
      })
      return false
    }
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      toast.show({
        icon: 'fail',
        content: '图片大小不能超过 2MB！',
      })
      return false
    }

    // 检查数量限制
    if (uploadFile.length >= MAX_UPLOAD_COUNT) {
      toast.show({
        icon: 'fail',
        content: getI18nString('max_upload_tip', { key: MAX_UPLOAD_COUNT, key2: 1 }),
      })
      return false
    }

    return isImage && isLt2M
  }

  /**
   * 返回上一步
   */
  const { run: handlePrevious } = useDebounceFn(() => {
    onClose()
    // 添加延迟，确保当前抽屉完全关闭后再打开上一个抽屉
    setTimeout(() => {
      handleReturnOrder()
    }, 300)
  })

  return (
    <>
      <CustomDrawer
        title={getI18nString('return_reason')}
        onClose={onClose}
        open={visible}
        footer={
          <div className="flex items-center gap-base-16">
            <Button
              style={{ width: '144px' }}
              onClick={isFromReturnPopup ? handlePrevious : onClose}>
              {isFromReturnPopup ? getI18nString('previous') : getI18nString('cancel')}
            </Button>
            <Button type="primary" style={{ flex: 1 }} onClick={handleCancelOrder}>
              {getI18nString('confirm')}
            </Button>
          </div>
        }>
        <div className="pt-base-24">
          <div className="space-y-[40px]">
            <div>
              <div className="mb-base-16 flex h-[41px] items-center gap-base text-xl">
                <span>{getI18nString('return_reason')}</span>
                <span className="text-primary">*</span>
              </div>
              <div className="flex flex-col gap-[29px]">
                {REASONS.map((option) => (
                  <Checkbox
                    key={option}
                    className="reason-checkbox"
                    checked={reason === option}
                    onChange={() => setReason(option)}>
                    {option}
                  </Checkbox>
                ))}
              </div>
            </div>

            {isShowAddressByShippingMethod && addressId ? (
              <div>
                <div className="mb-base-16 flex items-center justify-between">
                  <span className="text-[18px] leading-[140%] text-[#0F0F0F]">
                    {getI18nString('my_sending_address')}
                  </span>
                  <div className="flex items-center">
                    <svg
                      width="20"
                      height="21"
                      viewBox="0 0 20 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.1681 3.05841L4.18896 10.0378V13.2785H7.42971L14.4089 6.29915L11.1681 3.05841Z"
                        stroke="#DA291C"
                        strokeWidth="1.66667"
                      />
                      <path d="M3.33301 16.9609H16.6663" stroke="#DA291C" strokeWidth="1.66667" />
                    </svg>
                    <button
                      className="font-miSansDemiBold450 text-[16px] leading-[140%] text-[#DA291C]"
                      onClick={handleChangeAddress}>
                      管理
                    </button>
                  </div>
                </div>
                <div className="rounded-base border border-[#E1E1E4] px-base py-base-16 transition-colors">
                  <div className="flex gap-base">
                    <Location />
                    <div className="flex-1">
                      <div className="flex flex-wrap items-center gap-[4px]">
                        <span className="font-miSansDemiBold450 text-[18px] leading-none text-[#0F0F0F]">
                          {shippingAddress?.receive_name}
                        </span>
                        <span className="font-miSansDemiBold450 text-[18px] leading-none text-[#0F0F0F]">
                          {shippingAddress?.receive_phone}
                        </span>
                        {shippingAddress?.is_default && (
                          <span className="rounded-[4px] bg-primary p-[4px] font-miSansRegular330 text-[12px] leading-[100%] text-white">
                            {getI18nString('default_address')}
                          </span>
                        )}
                      </div>
                      <div className="mt-base line-clamp-2 break-all font-miSansRegular330 text-[16px] leading-[1.4] text-[#444446]">
                        {shippingAddress?.province} {shippingAddress?.city}{' '}
                        {shippingAddress?.county}
                        {shippingAddress?.street}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}

            <div>
              <div className="mb-base-16 text-[18px] leading-[1.4]">
                {getI18nString('add_description')}
              </div>
              <div className="rounded-base-12 border border-[#E1E1E4] p-base-12">
                <div className="relative mb-base-16 w-full">
                  <Input.TextArea
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    maxLength={200}
                    style={{ height: 120, resize: 'none' }}
                    placeholder="请输入您的评价～"
                    variant="borderless"
                  />
                  <span className="absolute bottom-2 right-4 text-[14px] text-[#888]">
                    {description.length}/200
                  </span>
                </div>

                <Upload
                  rootClassName="return-picture-card"
                  listType="picture-card"
                  fileList={uploadFile}
                  onChange={() => {
                    // 我们不在这里更新uploadFile，避免重复上传
                  }}
                  beforeUpload={(file) => {
                    // 检查文件是否已在处理中
                    if (processingFiles.has(file.name)) {
                      return false
                    }

                    // 检查是否已经上传过相同文件名的文件
                    const alreadyUploaded = uploadFile.some(
                      (item) => item.name === file.name && item.size === file.size,
                    )

                    if (alreadyUploaded) {
                      return false
                    }

                    if (beforeUpload(file)) {
                      handleUpload(file)
                        .then((result) => {
                          console.log('文件上传成功并添加到状态:', result.name)
                        })
                        .catch((err) => {
                          console.error('文件上传失败:', err)
                          if (err.message !== '文件已在处理中') {
                            toast.show({
                              icon: 'fail',
                              content: '上传失败',
                            })
                          }
                        })
                    }
                    return false // 阻止Upload组件内部上传流程
                  }}
                  onRemove={handleRemove}
                  disabled={uploading}
                  className={`return-image-uploader ${uploadFile.length > 0 ? '' : 'uploader-full'}`}
                  itemRender={(originNode, file, fileList, actions) => (
                    <div className="relative h-[106px] w-[106px]">
                      {file.thumbUrl || file.url ? (
                        <Image
                          src={file.thumbUrl || file.url || ''}
                          alt={file.name || ''}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="flex h-[106px] w-[106px] items-center justify-center bg-gray-100"></div>
                      )}
                      <div className="absolute right-1 top-1">
                        <CloseIcon onClick={() => actions.remove()} />
                      </div>
                    </div>
                  )}>
                  {uploadFile.length >= MAX_UPLOAD_COUNT ? null : (
                    <div className="flex flex-col items-center justify-center">
                      {uploading ? (
                        <div className="flex flex-col items-center">
                          <div className="mb-2 h-8 w-8 animate-spin rounded-full border-2 border-t-primary"></div>
                          <div className="text-[14px] text-[#888]">上传中...</div>
                        </div>
                      ) : (
                        <>
                          <svg
                            width="32"
                            height="33"
                            viewBox="0 0 32 33"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                              d="M28 15.1667V7.5H21.4L19 4.5H13L10.6 7.5H6.66667H4V10.1333V26.1L17.3333 25.8"
                              stroke="black"
                              strokeWidth="2.66667"
                            />
                            <ellipse
                              cx="16.6667"
                              cy="15.7653"
                              rx="4.66667"
                              ry="4.66667"
                              stroke="black"
                              strokeWidth="2.66667"
                            />
                            <path
                              d="M20 21.7503L20 24.417L23.1105 24.417L24.4438 23.0837L23.1105 21.7503L20 21.7503Z"
                              fill="#DA291C"
                            />
                            <path
                              d="M26.6667 28.5L24 28.5L24 25.3895L25.3333 24.0562L26.6667 25.3895L26.6667 28.5Z"
                              fill="#DA291C"
                            />
                            <path
                              d="M24.0003 17.833L26.667 17.833L26.667 20.9435L25.3337 22.2768L24.0003 20.9435L24.0003 17.833Z"
                              fill="#DA291C"
                            />
                            <path
                              d="M30.667 24.5837L30.667 21.917L27.5565 21.917L26.2232 23.2503L27.5565 24.5837L30.667 24.5837Z"
                              fill="#DA291C"
                            />
                          </svg>

                          <div className="mt-[6px] text-[14px] leading-[1.2] text-black">
                            {uploadFile.length > 0
                              ? getI18nString('upload_image_count', {
                                  key: MAX_UPLOAD_COUNT - uploadFile.length,
                                })
                              : getI18nString('upload_image')}
                          </div>
                        </>
                      )}
                    </div>
                  )}
                </Upload>
              </div>
            </div>
          </div>
        </div>
      </CustomDrawer>

      {/* 地址管理弹窗 */}
      <AddressManagementDrawer
        visible={isAddressManagementOpen}
        onClose={() => setIsAddressManagementOpen(false)}
        onOpen={() => setIsAddressManagementOpen(true)}
      />
    </>
  )
}

// 主组件，提供 LocalToastProvider
export default function ReturnApplicationDrawer(props: ReturnApplicationDrawerProps) {
  const containerRef = useRef<HTMLDivElement>(null)

  return (
    <LocalToastProvider
      containerRef={containerRef}
      position="center"
      contentClassName="gap-[8px] px-[16px] py-[12px]"
      textClassName="text-[16px] leading-[1.4]"
      iconWidth={22}
      iconHeight={22}>
      <div ref={containerRef} className="relative">
        <ReturnApplicationContent {...props} />
      </div>
    </LocalToastProvider>
  )
}
