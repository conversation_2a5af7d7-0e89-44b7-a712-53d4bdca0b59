'use client'

import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import type { StoreList as StoreListType, StoreListItem } from '@ninebot/core'
import { formatDistance, Map, useDebounceFn, useToastContext } from '@ninebot/core'
import { useLocation } from '@ninebot/core/src/businessHooks'
import type { MassMarkerPoint } from '@ninebot/core/src/types/amap'

import { Modal, Skeleton } from '@/components'

import { Confirm } from '../Confirm'

import AddressSelector from './AddressSelector'
import { performanceMonitor } from './PerformanceMonitor'
import StoreList from './StoreList'

// ==================== 类型定义 ====================

/**
 * 地址信息接口
 */
interface Address {
  region: string
  city: string
  district: string
}

/**
 * 组件属性接口
 */
interface StoreSelectorModalProps {
  doorVisible: boolean
  setDoorVisible: (visible: boolean) => void
  selectStore?: StoreListItem | null
  productId: string
  onConfirmCallback: (store: StoreListItem | null) => void
}

/**
 * 模态框状态枚举
 */
enum ModalState {
  LOADING = 'loading',
  LOCATION_PERMISSION = 'location_permission', // 定位权限
  ADDRESS_SELECTION = 'address_selection', // 地址选择
  STORE_SELECTION = 'store_selection', // 门店选择
}

// ==================== 常量定义 ====================

/**
 * 定位服务弹窗配置
 */
const LOCATION_MODAL_CONFIG = {
  title: '需要获取您的位置信息',
  content: '请授权定位信息以获取附近门店',
  cancelText: '取消',
  confirmText: '去设置',
} as const

// ==================== 图标组件 ====================

/**
 * 箭头图标组件
 */
const StoreSelectorArrow = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M2.56758 7.42851L3.74609 6.25L9.63865 12.1426L9.63865 13.3211L8.46014 13.3211L2.56758 7.42851Z"
      fill="#DA291C"
    />
    <path
      d="M17.4324 7.42851L16.2539 6.25L10.3614 12.1426L10.3614 13.3211L11.5399 13.3211L17.4324 7.42851Z"
      fill="#DA291C"
    />
  </svg>
)

// ==================== 主组件 ====================

/**
 * 门店选择器模态框组件
 *
 * 功能：
 * 1. 自动获取用户位置并显示附近门店
 * 2. 支持手动选择地址
 * 3. 支持门店列表展示和选择
 * 4. 支持定位权限引导
 * 5. 完善的错误处理和状态管理
 */
const StoreSelectorModal = ({
  doorVisible,
  setDoorVisible,
  productId,
  onConfirmCallback,
}: StoreSelectorModalProps) => {
  const getI18nString = useTranslations('Common')
  const { getLocation, reverseGeocode, openBrowserSettings } = useLocation()
  const toast = useToastContext()

  // ==================== 状态管理 ====================

  // 组件状态
  const [modalState, setModalState] = useState<ModalState>(ModalState.LOADING)

  // 数据状态
  const [store, setStore] = useState<StoreListType>([])
  const [curStore, setCurStore] = useState<StoreListItem | null>(null)
  const [address, setAddress] = useState<Address>({
    region: '',
    city: '',
    district: '',
  })
  const [userLocation, setUserLocation] = useState<{ longitude: number; latitude: number } | null>(
    null,
  )
  const [mapCenter, setMapCenter] = useState<{ longitude: number; latitude: number } | undefined>(
    undefined,
  )

  // UI状态
  const [btnLoading, setBtnLoading] = useState(false)
  const [initLoading, setInitLoading] = useState(true)

  // ==================== 工具函数 ====================

  /**
   * 显示定位服务弹窗
   */
  const showLocationServiceModal = useCallback(async () => {
    const shouldOpenSettings = await Confirm.confirm({
      title: LOCATION_MODAL_CONFIG.title,
      content: LOCATION_MODAL_CONFIG.content,
      okText: LOCATION_MODAL_CONFIG.confirmText,
      cancelText: LOCATION_MODAL_CONFIG.cancelText,
    })

    if (shouldOpenSettings) {
      setDoorVisible(false)
      openBrowserSettings()
    } else {
      // 用户拒绝定位，进入手动选择地址模式
      setModalState(ModalState.ADDRESS_SELECTION)
      setBtnLoading(true)
      setDoorVisible(true)
    }
  }, [setDoorVisible, openBrowserSettings])

  // ==================== 事件处理函数 ====================

  /**
   * 获取用户位置
   */
  const handleGetLocation = useCallback(async () => {
    performanceMonitor.start('门店选择器-手动获取位置')

    try {
      const result = await getLocation()
      performanceMonitor.end('门店选择器-手动获取位置', {
        成功: !!result,
        有坐标: !!result?.latitude,
      })

      if (result && result.latitude) {
        performanceMonitor.record('门店选择器-切换到门店选择模式', {
          触发方式: '手动获取位置成功',
        })
        // 获取到定位信息，进入门店选择模式
        setModalState(ModalState.STORE_SELECTION)
        setBtnLoading(true)
      } else {
        performanceMonitor.record('门店选择器-显示定位权限引导', {
          原因: '位置信息获取失败',
        })
        // 未授权定位信息，显示引导弹窗
        showLocationServiceModal()
      }
    } catch (error) {
      performanceMonitor.record('门店选择器-定位异常处理', {
        错误信息: String(error),
      })
      console.warn('获取定位信息失败，将使用手动选择地址:', error)

      // 定位失败时，进入手动选择地址模式
      setModalState(ModalState.ADDRESS_SELECTION)
      setBtnLoading(true)
      setDoorVisible(true)

      toast.show({
        icon: 'info',
        content: '定位失败，请手动选择门店地址',
      })
    }
  }, [getLocation, showLocationServiceModal, setDoorVisible, toast])

  /**
   * 关闭模态框
   */
  const handleCloseModal = useCallback(async () => {
    performanceMonitor.record('门店选择器-关闭弹窗')

    // 生成性能报告
    performanceMonitor.generateReport()

    setDoorVisible(false)

    // 重置所有状态
    setModalState(ModalState.LOADING)
    setStore([])
    setCurStore(null)
    setBtnLoading(false)
    setInitLoading(true)
    setUserLocation(null)
    setAddress({
      region: '',
      city: '',
      district: '',
    })
    setMapCenter(undefined)

    // 清空性能监控数据，为下次使用做准备
    performanceMonitor.clear()
  }, [setDoorVisible])

  /**
   * 确认门店选择
   */
  const handleConfirmStore = useCallback(() => {
    performanceMonitor.start('门店选择器-确认门店选择')

    if (curStore) {
      performanceMonitor.record('门店选择器-门店选择成功', {
        门店名称: curStore.store_name,
        门店ID: curStore.store_id,
        门店地址: curStore.store_address,
      })

      // 生成性能报告
      performanceMonitor.generateReport()

      // 更新选中的门店
      setDoorVisible(false)
      onConfirmCallback(curStore)
      performanceMonitor.end('门店选择器-确认门店选择', { 结果: '成功' })

      // 清空性能监控数据
      performanceMonitor.clear()
    } else if (address.district && store.length === 0) {
      performanceMonitor.record('门店选择器-无门店直接关闭', {
        地址: `${address.region} ${address.city} ${address.district}`,
        门店数量: store.length,
      })

      // 生成性能报告
      performanceMonitor.generateReport()

      // 选择了地址但没有门店，直接关闭
      setDoorVisible(false)
      performanceMonitor.end('门店选择器-确认门店选择', { 结果: '无门店关闭' })

      // 清空性能监控数据
      performanceMonitor.clear()
      return
    } else {
      performanceMonitor.record('门店选择器-提示选择门店', {
        当前门店: '未选择',
        可选门店数量: store.length,
      })
      // 提示用户选择门店
      toast.show({
        icon: 'info',
        content: getI18nString('product_select_store_tip'),
      })
      performanceMonitor.end('门店选择器-确认门店选择', { 结果: '提示选择' })
      return
    }
  }, [
    curStore,
    address.district,
    address.city,
    address.region,
    store.length,
    toast,
    getI18nString,
    onConfirmCallback,
    setDoorVisible,
  ])

  /**
   * 确认地址选择
   */
  const handleAddressConfirm = useCallback(async () => {
    performanceMonitor.start('门店选择器-地址确认切换')
    performanceMonitor.record('门店选择器-地址确认', {
      选择的地址: `${address.region} ${address.city} ${address.district}`,
    })

    setStore([])
    setCurStore(null)
    setBtnLoading(true)
    setModalState(ModalState.STORE_SELECTION)

    performanceMonitor.end('门店选择器-地址确认切换')
  }, [address.region, address.city, address.district])

  /**
   * 重新选择地址
   */
  const handleShowAddressSelection = useCallback(() => {
    performanceMonitor.record('门店选择器-重新选择地址', {
      当前地址: `${address.region} ${address.city} ${address.district}`,
      当前门店数量: store.length,
    })

    setModalState(ModalState.ADDRESS_SELECTION)
    setStore([])
    setCurStore(null)
    setMapCenter(undefined)
  }, [address.region, address.city, address.district, store.length])

  /**
   * 地址级联选择回调
   */
  const handleAddressChange = useCallback(
    (province: { label: string }, city: { label: string }, district: { label: string }) => {
      performanceMonitor.record('门店选择器-地址级联选择', {
        省份: province?.label || '',
        城市: city?.label || '',
        区县: district?.label || '',
        是否完整: !!(province?.label && city?.label && district?.label),
      })

      const hasProvince = !!province?.label
      setBtnLoading(!hasProvince)

      setAddress({
        region: province?.label || '',
        city: city?.label || '',
        district: district?.label || '',
      })
    },
    [],
  )

  /**
   * 处理地图标记点击
   */
  const handleMarkerClick = useCallback(
    (point: MassMarkerPoint) => {
      performanceMonitor.start('门店选择器-地图标记点击')

      performanceMonitor.start('门店选择器-查找对应门店')
      // 根据点击的标记点找到对应的门店
      const clickedStore = store.find((storeItem) => {
        if (!storeItem) return false
        const storeLng = Number(storeItem.store_longitude)
        const storeLat = Number(storeItem.store_latitude)
        return storeLng === point.lnglat[0] && storeLat === point.lnglat[1]
      })
      performanceMonitor.end('门店选择器-查找对应门店', {
        门店总数: store.length,
        找到门店: !!clickedStore,
      })

      if (clickedStore) {
        performanceMonitor.start('门店选择器-门店选中和滚动')

        // 选中对应的门店
        setCurStore(clickedStore)

        // 滚动到对应的门店项
        const element = document.getElementById(`store-item-${clickedStore.store_id}`)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
        }

        performanceMonitor.end('门店选择器-门店选中和滚动', {
          门店名称: clickedStore.store_name,
          门店ID: clickedStore.store_id,
          找到DOM元素: !!element,
        })
      }

      performanceMonitor.end('门店选择器-地图标记点击')
    },
    [store, setCurStore],
  )

  // ==================== 计算属性 ====================

  /**
   * 当前是否显示地址选择器
   */
  const showAddressSelector = useMemo(() => {
    return modalState === ModalState.ADDRESS_SELECTION
  }, [modalState])

  /**
   * 当前是否显示门店列表
   */
  const showStoreList = useMemo(() => {
    return modalState === ModalState.STORE_SELECTION
  }, [modalState])

  /**
   * 当前是否显示定位引导
   */
  const showLocationGuide = useMemo(() => {
    return modalState === ModalState.LOCATION_PERMISSION
  }, [modalState])

  /**
   * 按钮文本
   */
  const buttonText = useMemo(() => {
    if (showLocationGuide) return getI18nString('product_get_location')
    return getI18nString('confirm')
  }, [showLocationGuide, getI18nString])

  /**
   * 按钮点击处理函数
   */
  const buttonClickHandler = useMemo(() => {
    if (showLocationGuide) return handleGetLocation
    if (showStoreList) return handleConfirmStore
    return handleAddressConfirm
  }, [
    showLocationGuide,
    showStoreList,
    handleGetLocation,
    handleConfirmStore,
    handleAddressConfirm,
  ])

  /**
   * 按钮是否禁用
   */
  const isButtonDisabled = useMemo(() => {
    if (showLocationGuide) return false
    if (showStoreList) return btnLoading || !curStore
    return btnLoading
  }, [showLocationGuide, showStoreList, btnLoading, curStore])

  // ==================== 初始化逻辑 ====================

  /**
   * 初始化组件
   */
  const initializeComponent = useCallback(async () => {
    performanceMonitor.start('门店选择器-完整初始化流程')

    try {
      performanceMonitor.start('门店选择器-状态重置')
      setStore([])
      setInitLoading(true)

      // 重置当前选中的门店，让用户重新选择
      setCurStore(null)
      performanceMonitor.end('门店选择器-状态重置')

      // 每次都尝试重新获取当前位置
      try {
        performanceMonitor.start('门店选择器-获取用户位置', {
          方法: 'getLocation API',
        })
        const currentLocation = await getLocation()
        performanceMonitor.end('门店选择器-获取用户位置', {
          成功: !!currentLocation,
          有坐标: !!(currentLocation?.latitude && currentLocation?.longitude),
        })

        if (currentLocation && currentLocation.latitude && currentLocation.longitude) {
          performanceMonitor.record('门店选择器-位置信息处理', {
            经度: currentLocation.longitude,
            纬度: currentLocation.latitude,
          })

          // 保存位置信息
          setUserLocation({
            longitude: currentLocation.longitude,
            latitude: currentLocation.latitude,
          })

          // 同时设置地图中心点
          setMapCenter({
            longitude: currentLocation.longitude,
            latitude: currentLocation.latitude,
          })

          // 通过逆地理编码获取地址
          performanceMonitor.start('门店选择器-逆地理编码', {
            经度: currentLocation.longitude,
            纬度: currentLocation.latitude,
          })
          const res = await reverseGeocode(currentLocation.latitude, currentLocation.longitude)
          performanceMonitor.end('门店选择器-逆地理编码')

          const addr = {
            region: res.regeocode.addressComponent.province,
            city: res.regeocode.addressComponent.city,
            district: res.regeocode.addressComponent.district,
          }

          performanceMonitor.record('门店选择器-地址信息设置', {
            省份: addr.region,
            城市: addr.city,
            区县: addr.district,
          })

          setAddress(addr)
          setModalState(ModalState.STORE_SELECTION)
        } else {
          performanceMonitor.record('门店选择器-进入手动地址选择', {
            原因: '未获取到位置信息',
          })
          // 未获取到位置信息，进入手动选择地址模式
          setUserLocation(null)
          setMapCenter(undefined)
          setModalState(ModalState.ADDRESS_SELECTION)
          setBtnLoading(true)
          setAddress({
            region: '',
            city: '',
            district: '',
          })
        }
      } catch (error) {
        performanceMonitor.record('门店选择器-定位失败处理', {
          错误信息: String(error),
        })
        console.warn('获取位置信息失败，将使用手动选择地址:', error)
        // 定位失败，进入手动选择地址模式
        setUserLocation(null)
        setMapCenter(undefined)
        setModalState(ModalState.ADDRESS_SELECTION)
        setBtnLoading(true)
        setAddress({
          region: '',
          city: '',
          district: '',
        })
      }
    } catch (error) {
      performanceMonitor.record('门店选择器-初始化异常', {
        错误信息: String(error),
      })
      console.error('初始化门店选择器失败:', error)
      toast.show({
        icon: 'fail',
        content: '定位失败，请手动选择门店',
      })

      // 初始化失败，进入地址选择模式
      setUserLocation(null)
      setMapCenter(undefined)
      setModalState(ModalState.ADDRESS_SELECTION)
      setBtnLoading(true)
    } finally {
      setInitLoading(false)
      performanceMonitor.end('门店选择器-完整初始化流程')
    }
  }, [getLocation, reverseGeocode, toast])

  // ==================== 副作用 ====================

  /**
   * 监听弹窗显示状态，初始化组件
   */
  useEffect(() => {
    if (doorVisible) {
      performanceMonitor.record('门店选择器-弹窗打开', {
        产品ID: productId,
        时间戳: new Date().toISOString(),
      })
      initializeComponent()
    }
  }, [doorVisible, initializeComponent, productId])

  // 防抖的地图中心点更新函数
  const updateMapCenterForStore = useCallback((store: StoreListItem) => {
    if (store && store.store_latitude && store.store_longitude) {
      performanceMonitor.record('门店选择器-地图中心点更新', {
        门店名称: store.store_name,
        新中心点: {
          经度: Number(store.store_longitude),
          纬度: Number(store.store_latitude),
        },
      })

      setMapCenter({
        longitude: Number(store.store_longitude),
        latitude: Number(store.store_latitude),
      })
    }
  }, [])

  const { run: debouncedUpdateMapCenter } = useDebounceFn(updateMapCenterForStore, { wait: 300 })

  // 更新地图中心点为当前选中的门店位置（使用防抖）
  useEffect(() => {
    if (curStore) {
      debouncedUpdateMapCenter(curStore)
    }
  }, [curStore, debouncedUpdateMapCenter])

  // 防抖的默认地图中心点设置函数
  const setDefaultMapCenter = useCallback(
    (storeList: StoreListType) => {
      if (!userLocation && !mapCenter && storeList && storeList.length > 0) {
        const firstStore = storeList[0]
        if (firstStore && firstStore.store_latitude && firstStore.store_longitude) {
          performanceMonitor.record('门店选择器-设置默认地图中心', {
            门店名称: firstStore.store_name,
            门店总数: storeList.length,
            默认中心点: {
              经度: Number(firstStore.store_longitude),
              纬度: Number(firstStore.store_latitude),
            },
          })

          setMapCenter({
            longitude: Number(firstStore.store_longitude),
            latitude: Number(firstStore.store_latitude),
          })
        }
      }
    },
    [userLocation, mapCenter],
  )

  const { run: debouncedSetDefaultMapCenter } = useDebounceFn(setDefaultMapCenter, { wait: 500 })

  // 当门店数据更新时，如果没有用户位置和地图中心，使用第一个门店的位置作为地图中心（使用防抖）
  useEffect(() => {
    debouncedSetDefaultMapCenter(store)
  }, [store, debouncedSetDefaultMapCenter])

  // 将所有可用门店和当前选中门店合并为海量点数据
  const storePoints = useMemo(() => {
    performanceMonitor.start('门店选择器-生成地图标记点')

    const points: MassMarkerPoint[] = []

    if (store && store.length > 0) {
      store.forEach((item) => {
        if (!item) return
        points.push({
          lnglat: [Number(item.store_longitude), Number(item.store_latitude)],
          name: item.store_name || '未知门店',
          // 如果是当前选中的门店，使用样式索引1（选中样式），否则使用默认样式0
          style: curStore && item.store_id === curStore.store_id ? 1 : 0,
        })
      })
    }

    performanceMonitor.end('门店选择器-生成地图标记点', {
      门店总数: store.length,
      标记点数量: points.length,
      选中门店: curStore?.store_name || '无',
    })

    return points
  }, [store, curStore])

  // ==================== 渲染函数 ====================

  /**
   * 渲染加载状态
   */
  const renderLoadingContent = () => (
    <div className="flex h-[448px] gap-base-16">
      {/* 左侧地图区域骨架屏 */}
      <div className="flex w-[600px] flex-col rounded-[4px]">
        {/* 地图骨架屏 */}
        <div className="flex-1">
          <Skeleton
            shape="square"
            style={{
              width: '100%',
              height: '100%',
              borderRadius: 12,
              backgroundColor: '#F3F3F4',
            }}
          />
        </div>
      </div>

      {/* 右侧内容区域骨架屏 */}
      <div className="flex flex-1 flex-col gap-base-16">
        {/* 地址显示骨架屏 */}
        <div className="flex flex-col gap-base">
          <Skeleton
            style={{
              width: 120,
              height: 20,
              borderRadius: 6,
              backgroundColor: '#F3F3F4',
            }}
          />
          <Skeleton
            style={{
              width: 200,
              height: 24,
              borderRadius: 8,
              backgroundColor: '#F3F3F4',
            }}
          />
        </div>

        {/* 内容区域骨架屏 */}
        <div className="flex-1 space-y-4">
          {Array.from({ length: 7 }, (_, index) => (
            <div key={index}>
              <Skeleton
                style={{
                  width: '100%',
                  height: 40,
                  borderRadius: 12,
                  backgroundColor: '#F3F3F4',
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )

  /**
   * 渲染定位引导状态
   */
  const renderLocationGuideContent = () => (
    <div className="flex h-[448px] items-center justify-center">
      <div className="text-center text-gray-500">
        <div className="mb-4 text-lg">目前还没有定位权限哦～</div>
        <div className="text-sm">请授权定位信息以获取附近门店</div>
      </div>
    </div>
  )

  /**
   * 渲染主要内容
   */
  const renderMainContent = () => (
    <div className="flex h-[448px] gap-base-16">
      {/* 左侧地图区域 */}
      <div className="flex w-[600px] flex-col rounded-lg bg-gray-50">
        {curStore && (
          <div className="space-y-4 bg-white pb-base-24">
            <div className="font-miSansMedium380 text-2xl">{curStore.store_name}</div>
            <div className="text-[#444446]">
              <div>
                {getI18nString('product_store_business')}{' '}
                {curStore.business_hours + '-' + curStore.closing_hours}
              </div>
              <div>
                {Number(curStore.store_distance) ? (
                  <span>
                    {getI18nString('product_store_distance')}
                    {formatDistance(Number(curStore.store_distance))}
                    {' | '}
                  </span>
                ) : null}
                {curStore.store_address}
              </div>
            </div>
          </div>
        )}
        <div className="flex-1">
          <Map
            mapCenter={mapCenter}
            points={storePoints}
            onMarkerClick={handleMarkerClick}
            zoom={curStore ? 17 : undefined}
          />
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="flex flex-1 flex-col gap-base-16">
        {/* 地址显示和选择 */}
        <div className="flex flex-col gap-base">
          <span className="text-[#444446]">{getI18nString('product_nearby_stores')}：</span>
          <div className="flex min-h-base-16 items-center gap-base">
            {address?.region ? (
              <>
                <button
                  className="flex items-center text-[#DA291C] hover:opacity-80"
                  onClick={handleShowAddressSelection}
                  aria-label="重新选择地区">
                  {address?.region} {address?.city} {address?.district}
                </button>
                {showStoreList && <StoreSelectorArrow />}
              </>
            ) : (
              <span className="text-[#444446]">请选择地址</span>
            )}
          </div>
        </div>

        {/* 门店列表 */}
        {showStoreList && (
          <StoreList
            productId={productId}
            store={store}
            setStore={setStore}
            setBtnLoading={setBtnLoading}
            curStore={curStore}
            setCurStore={setCurStore}
            address={address}
            userLocation={userLocation}
          />
        )}

        {/* 地址选择器 */}
        {showAddressSelector && (
          <AddressSelector
            onSelect={handleAddressChange}
            defaultValue={{
              province: address.region,
              city: address.city,
              district: address.district,
            }}
          />
        )}
      </div>
    </div>
  )

  /**
   * 渲染内容
   */
  const renderContent = () => {
    if (initLoading) {
      return renderLoadingContent()
    }

    if (showLocationGuide) {
      return renderLocationGuideContent()
    }

    return renderMainContent()
  }

  // ==================== 组件返回 ====================

  return (
    <Modal
      isOpen={doorVisible}
      onClose={handleCloseModal}
      onConfirm={buttonClickHandler}
      title={getI18nString('product_select_store')}
      width={944}
      okButtonProps={{
        disabled: isButtonDisabled,
      }}
      okText={buttonText}>
      {renderContent()}
    </Modal>
  )
}

export default StoreSelectorModal
