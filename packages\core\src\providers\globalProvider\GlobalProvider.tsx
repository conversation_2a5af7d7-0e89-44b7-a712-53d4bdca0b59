'use client'

import { useEffect } from 'react'

import { useGetStoreConfigQuery } from '../../services'
import { gustIdSelector, setGustId, setStoreConfig } from '../../store'
import { useAppDispatch, useAppSelector } from '../../store/hooks'
import { TBaseComponentProps } from '../../typings'
import { generateGustId } from '../../utils/helper'
type TGlobalProvider = TBaseComponentProps & {
  isPurePage?: boolean
}

/**
 * 全局 Provider
 */
const GlobalProvider = ({ children, isPurePage = false }: TGlobalProvider) => {
  const dispatch = useAppDispatch()

  const { data: storeConfigData } = useGetStoreConfigQuery(
    {},
    {
      skip: isPurePage,
    },
  )
  const gustId = useAppSelector(gustIdSelector)

  /**
   * 控制 loading
   */
  // useEffect(() => {
  //   if (storeConfigLoading) {
  //     loading.show()
  //   } else {
  //     loading.hide()
  //   }
  // }, [storeConfigLoading, loading])

  /**
   * 保存 storeConfig 数据
   */
  useEffect(() => {
    if (storeConfigData?.storeConfig) {
      dispatch(setStoreConfig(storeConfigData.storeConfig))
    }
  }, [dispatch, storeConfigData])

  /**
   * 保存 gustId 数据
   */
  useEffect(() => {
    if (!gustId) {
      const NewGustId = generateGustId()
      dispatch(setGustId(NewGustId))
    }
  }, [dispatch, gustId])

  // useEffect(() => {
  //   const loadVConsole = async () => {
  //     const VConsole = (await import('vconsole')).default
  //     try {
  //       new VConsole()
  //     } catch (err) {
  //       console.log(err)
  //     }
  //   }

  //   loadVConsole()
  // }, [])

  return <>{children}</>
}

export default GlobalProvider
